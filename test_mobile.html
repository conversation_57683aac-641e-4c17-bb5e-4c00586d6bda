<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端适配测试 - 临时邮箱</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="static/css/styles.css">
    <style>
        /* 测试用的样式 */
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 2px dashed #e5e7eb;
            border-radius: 8px;
        }
        
        .test-title {
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 1rem;
        }
        
        .device-info {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <header class="bg-blue-600 text-white shadow-md">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-mobile-alt text-2xl mr-2"></i>
                    <h1 class="text-xl font-bold">移动端适配测试</h1>
                </div>
                <nav>
                    <ul class="flex space-x-6 items-center">
                        <li><a href="/" class="hover:underline">返回主页</a></li>
                        <li class="relative">
                            <select id="language-selector" class="bg-blue-500 text-white border border-blue-400 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-300">
                                <option value="zh-CN">中文</option>
                                <option value="en-US">English</option>
                            </select>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">移动端功能测试</h2>

            <!-- 设备信息 -->
            <div class="device-info">
                <div><strong>设备信息:</strong></div>
                <div>屏幕宽度: <span id="screen-width"></span>px</div>
                <div>屏幕高度: <span id="screen-height"></span>px</div>
                <div>视口宽度: <span id="viewport-width"></span>px</div>
                <div>视口高度: <span id="viewport-height"></span>px</div>
                <div>触摸支持: <span id="touch-support"></span></div>
                <div>用户代理: <span id="user-agent"></span></div>
            </div>

            <!-- 按钮触摸测试 -->
            <div class="test-section">
                <div class="test-title">1. 按钮触摸区域测试 (最小44px×44px)</div>
                <div class="button-group">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-copy mr-2"></i>复制测试
                    </button>
                    <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-plus mr-2"></i>新邮箱测试
                    </button>
                    <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                        <i class="fas fa-edit mr-2"></i>自定义测试
                    </button>
                </div>
            </div>

            <!-- 邮箱地址长按测试 -->
            <div class="test-section">
                <div class="test-title">2. 邮箱地址长按复制测试</div>
                <div class="email-box bg-gray-100 p-4 rounded-lg long-press-hint haptic-feedback">
                    <span class="text-blue-600 font-bold break-all"><EMAIL></span>
                </div>
                <p class="text-sm text-gray-600 mt-2">长按邮箱地址测试复制功能</p>
            </div>

            <!-- 邮件列表滑动测试 -->
            <div class="test-section">
                <div class="test-title">3. 邮件列表滑动操作测试</div>
                <div class="message-list pull-to-refresh scroll-smooth" style="max-height: 300px;">
                    <div class="pull-to-refresh-indicator">
                        <i class="fas fa-arrow-down text-blue-500"></i>
                    </div>
                    
                    <div class="space-y-2">
                        <div class="email-item p-3 border-b hover:bg-gray-50 cursor-pointer haptic-feedback transition-transform duration-200" data-id="1">
                            <div class="email-content relative">
                                <div class="flex justify-between items-start">
                                    <div class="font-medium">测试邮件标题 1</div>
                                    <div class="text-xs text-gray-500">2分钟前</div>
                                </div>
                                <div class="text-sm text-gray-600">从: <EMAIL></div>
                                <div class="text-sm mt-1 text-gray-700">这是一封测试邮件，用于测试滑动操作功能...</div>
                            </div>
                        </div>
                        
                        <div class="email-item p-3 border-b hover:bg-gray-50 cursor-pointer haptic-feedback transition-transform duration-200" data-id="2">
                            <div class="email-content relative">
                                <div class="flex justify-between items-start">
                                    <div class="font-medium">测试邮件标题 2</div>
                                    <div class="text-xs text-gray-500">5分钟前</div>
                                </div>
                                <div class="text-sm text-gray-600">从: <EMAIL></div>
                                <div class="text-sm mt-1 text-gray-700">另一封测试邮件，可以向左滑动查看操作选项...</div>
                            </div>
                        </div>
                        
                        <div class="email-item p-3 border-b hover:bg-gray-50 cursor-pointer haptic-feedback transition-transform duration-200" data-id="3">
                            <div class="email-content relative">
                                <div class="flex justify-between items-start">
                                    <div class="font-medium">测试邮件标题 3</div>
                                    <div class="text-xs text-gray-500">10分钟前</div>
                                </div>
                                <div class="text-sm text-gray-600">从: <EMAIL></div>
                                <div class="text-sm mt-1 text-gray-700">第三封测试邮件，支持下拉刷新和滑动操作...</div>
                            </div>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 mt-2">向下拉动列表测试刷新，向左滑动邮件项测试操作菜单</p>
            </div>

            <!-- 自定义模态框测试 -->
            <div class="test-section">
                <div class="test-title">4. 自定义模态框测试</div>
                <button id="test-modal-btn" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px]">
                    <i class="fas fa-edit mr-2"></i>打开自定义前缀模态框
                </button>
                <p class="text-sm text-gray-600 mt-2">测试移动端友好的输入模态框</p>
            </div>

            <!-- 响应式布局测试 -->
            <div class="test-section">
                <div class="test-title">5. 响应式布局测试</div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-blue-100 p-4 rounded-lg text-center">
                        <h4 class="font-semibold">小屏幕</h4>
                        <p class="text-sm">320px-768px</p>
                    </div>
                    <div class="bg-green-100 p-4 rounded-lg text-center">
                        <h4 class="font-semibold">中等屏幕</h4>
                        <p class="text-sm">768px-1024px</p>
                    </div>
                    <div class="bg-purple-100 p-4 rounded-lg text-center">
                        <h4 class="font-semibold">大屏幕</h4>
                        <p class="text-sm">1024px+</p>
                    </div>
                </div>
            </div>

            <!-- 性能测试 -->
            <div class="test-section">
                <div class="test-title">6. 性能测试</div>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-gray-100 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">滚动性能</h4>
                        <div class="h-32 overflow-y-auto scroll-smooth bg-white p-2 rounded">
                            <div class="space-y-2">
                                <div class="h-8 bg-blue-100 rounded"></div>
                                <div class="h-8 bg-green-100 rounded"></div>
                                <div class="h-8 bg-purple-100 rounded"></div>
                                <div class="h-8 bg-yellow-100 rounded"></div>
                                <div class="h-8 bg-red-100 rounded"></div>
                                <div class="h-8 bg-indigo-100 rounded"></div>
                                <div class="h-8 bg-pink-100 rounded"></div>
                                <div class="h-8 bg-gray-100 rounded"></div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-100 p-4 rounded-lg">
                        <h4 class="font-semibold mb-2">动画性能</h4>
                        <div class="loading-pulse bg-blue-500 h-8 rounded mb-2"></div>
                        <div class="haptic-feedback bg-green-500 h-8 rounded cursor-pointer">点击测试</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 自定义前缀模态框 -->
    <div id="custom-prefix-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mobile-modal-content">
                <div class="flex justify-between items-center p-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-800">自定义邮箱前缀</h3>
                    <button id="close-custom-modal" class="text-gray-400 hover:text-gray-600 haptic-feedback min-h-[44px] min-w-[44px] flex items-center justify-center">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="p-4">
                    <div class="mb-4">
                        <label for="custom-prefix-input" class="block text-sm font-medium text-gray-700 mb-2">
                            邮箱前缀（1-20字符，支持字母、数字、连字符）
                        </label>
                        <input 
                            type="text" 
                            id="custom-prefix-input" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="例如：myemail123"
                            maxlength="20"
                            autocomplete="off"
                            autocapitalize="off"
                            spellcheck="false"
                        >
                        <div id="prefix-error" class="text-red-500 text-sm mt-1 hidden"></div>
                        <div class="text-gray-500 text-xs mt-1">
                            提示：前缀将用于生成您的临时邮箱地址
                        </div>
                    </div>
                    <div class="flex gap-3">
                        <button id="cancel-custom-btn" class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px]">
                            取消
                        </button>
                        <button id="confirm-custom-btn" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px]">
                            确认
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示设备信息
        function updateDeviceInfo() {
            document.getElementById('screen-width').textContent = screen.width;
            document.getElementById('screen-height').textContent = screen.height;
            document.getElementById('viewport-width').textContent = window.innerWidth;
            document.getElementById('viewport-height').textContent = window.innerHeight;
            document.getElementById('touch-support').textContent = 'ontouchstart' in window ? '是' : '否';
            document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 50) + '...';
        }

        // 测试模态框
        document.getElementById('test-modal-btn').addEventListener('click', () => {
            document.getElementById('custom-prefix-modal').classList.remove('hidden');
        });

        document.getElementById('close-custom-modal').addEventListener('click', () => {
            document.getElementById('custom-prefix-modal').classList.add('hidden');
        });

        document.getElementById('cancel-custom-btn').addEventListener('click', () => {
            document.getElementById('custom-prefix-modal').classList.add('hidden');
        });

        document.getElementById('confirm-custom-btn').addEventListener('click', () => {
            const input = document.getElementById('custom-prefix-input');
            alert('输入的前缀: ' + input.value);
            document.getElementById('custom-prefix-modal').classList.add('hidden');
        });

        // 初始化
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);

        // 模拟移动端功能
        console.log('移动端测试页面已加载');
    </script>

    <!-- 引入移动端增强模块 -->
    <script src="static/js/mobile.js"></script>
</body>
</html>
