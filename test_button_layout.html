<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮布局测试 - 临时邮箱</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="static/css/styles.css">
    <style>
        .test-container {
            margin: 2rem 0;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }
        
        .screen-info {
            background: #f3f4f6;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
        }
        
        .layout-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(59, 130, 246, 0.9);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: bold;
            z-index: 1000;
        }
        
        .button-test {
            background: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .expected-layout {
            color: #059669;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 布局指示器 -->
    <div class="layout-indicator" id="layout-indicator">
        桌面端 (>1024px)
    </div>

    <header class="bg-blue-600 text-white shadow-md">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-cog text-2xl mr-2"></i>
                    <h1 class="text-xl font-bold">按钮布局响应式测试</h1>
                </div>
                <div class="text-sm">
                    <span id="current-width"></span>px
                </div>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">按钮组响应式布局测试</h2>

            <!-- 屏幕信息 -->
            <div class="screen-info">
                <div><strong>当前屏幕信息:</strong></div>
                <div>视口宽度: <span id="viewport-width"></span>px</div>
                <div>视口高度: <span id="viewport-height"></span>px</div>
                <div>设备像素比: <span id="device-ratio"></span></div>
                <div>预期布局: <span id="expected-layout"></span></div>
            </div>

            <!-- 测试用例1: 标准按钮组 -->
            <div class="test-container">
                <h3 class="text-lg font-semibold mb-4">测试用例1: 标准按钮组</h3>
                <div class="button-test">
                    <div class="expected-layout" id="expected-1">预期布局: 水平排列</div>
                    <div class="button-group">
                        <button class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-sync-alt mr-2"></i>刷新
                        </button>
                        <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-plus mr-2"></i>新邮箱
                        </button>
                        <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-edit mr-2"></i>自定义
                        </button>
                        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-history mr-2"></i>历史记录
                        </button>
                        <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-trash mr-2"></i>删除邮箱
                        </button>
                    </div>
                </div>
            </div>

            <!-- 测试用例2: 少量按钮 -->
            <div class="test-container">
                <h3 class="text-lg font-semibold mb-4">测试用例2: 少量按钮</h3>
                <div class="button-test">
                    <div class="expected-layout" id="expected-2">预期布局: 水平排列</div>
                    <div class="button-group">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-copy mr-2"></i>复制
                        </button>
                        <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-plus mr-2"></i>新建
                        </button>
                        <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-trash mr-2"></i>删除
                        </button>
                    </div>
                </div>
            </div>

            <!-- 测试用例3: 长文本按钮 -->
            <div class="test-container">
                <h3 class="text-lg font-semibold mb-4">测试用例3: 长文本按钮</h3>
                <div class="button-test">
                    <div class="expected-layout" id="expected-3">预期布局: 水平排列（可换行）</div>
                    <div class="button-group">
                        <button class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-download mr-2"></i>下载邮件附件
                        </button>
                        <button class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-share mr-2"></i>分享邮箱地址
                        </button>
                        <button class="bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg transition-colors haptic-feedback min-h-[44px] flex items-center justify-center">
                            <i class="fas fa-cog mr-2"></i>高级设置选项
                        </button>
                    </div>
                </div>
            </div>

            <!-- 布局说明 -->
            <div class="test-container">
                <h3 class="text-lg font-semibold mb-4">响应式布局说明</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-red-100 p-4 rounded-lg text-center">
                        <h4 class="font-semibold text-red-800">小型手机</h4>
                        <p class="text-sm text-red-600">≤375px</p>
                        <p class="text-xs mt-2">单列布局</p>
                    </div>
                    <div class="bg-orange-100 p-4 rounded-lg text-center">
                        <h4 class="font-semibold text-orange-800">标准手机</h4>
                        <p class="text-sm text-orange-600">376px-428px</p>
                        <p class="text-xs mt-2">两列布局</p>
                    </div>
                    <div class="bg-yellow-100 p-4 rounded-lg text-center">
                        <h4 class="font-semibold text-yellow-800">小型平板</h4>
                        <p class="text-sm text-yellow-600">429px-768px</p>
                        <p class="text-xs mt-2">三列布局</p>
                    </div>
                    <div class="bg-green-100 p-4 rounded-lg text-center">
                        <h4 class="font-semibold text-green-800">桌面端</h4>
                        <p class="text-sm text-green-600">≥769px</p>
                        <p class="text-xs mt-2">水平排列</p>
                    </div>
                </div>
            </div>

            <!-- 调试信息 -->
            <div class="test-container">
                <h3 class="text-lg font-semibold mb-4">CSS调试信息</h3>
                <div class="bg-gray-100 p-4 rounded-lg">
                    <div class="mb-2">
                        <strong>当前应用的CSS规则:</strong>
                        <div id="css-rules" class="font-mono text-sm mt-2 bg-white p-2 rounded border"></div>
                    </div>
                    <button onclick="debugButtonGroup()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        检查按钮组样式
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        function updateScreenInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const ratio = window.devicePixelRatio || 1;
            
            document.getElementById('viewport-width').textContent = width;
            document.getElementById('viewport-height').textContent = height;
            document.getElementById('device-ratio').textContent = ratio;
            document.getElementById('current-width').textContent = width;
            
            // 更新布局指示器和预期布局
            let layoutType, expectedLayout;
            
            if (width <= 375) {
                layoutType = '小型手机 (≤375px)';
                expectedLayout = '单列布局';
            } else if (width <= 428) {
                layoutType = '标准手机 (376px-428px)';
                expectedLayout = '两列布局';
            } else if (width <= 768) {
                layoutType = '小型平板 (429px-768px)';
                expectedLayout = '三列布局';
            } else if (width <= 1024) {
                layoutType = '大型平板 (769px-1024px)';
                expectedLayout = '水平排列';
            } else {
                layoutType = '桌面端 (>1024px)';
                expectedLayout = '水平排列';
            }
            
            document.getElementById('layout-indicator').textContent = layoutType;
            document.getElementById('expected-layout').textContent = expectedLayout;
            
            // 更新所有测试用例的预期布局
            document.getElementById('expected-1').textContent = `预期布局: ${expectedLayout}`;
            document.getElementById('expected-2').textContent = `预期布局: ${expectedLayout}`;
            document.getElementById('expected-3').textContent = `预期布局: ${expectedLayout}（可换行）`;
        }
        
        function debugButtonGroup() {
            const buttonGroup = document.querySelector('.button-group');
            const styles = window.getComputedStyle(buttonGroup);
            
            const cssInfo = {
                display: styles.display,
                flexDirection: styles.flexDirection,
                flexWrap: styles.flexWrap,
                gridTemplateColumns: styles.gridTemplateColumns,
                gap: styles.gap,
                alignItems: styles.alignItems,
                justifyContent: styles.justifyContent
            };
            
            document.getElementById('css-rules').innerHTML = Object.entries(cssInfo)
                .map(([key, value]) => `<div><strong>${key}:</strong> ${value}</div>`)
                .join('');
        }
        
        // 初始化和监听窗口大小变化
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
        
        // 页面加载完成后检查样式
        window.addEventListener('load', debugButtonGroup);
        
        console.log('按钮布局测试页面已加载');
    </script>
</body>
</html>
