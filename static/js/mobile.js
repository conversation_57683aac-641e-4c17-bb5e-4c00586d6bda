/**
 * 移动端功能增强模块
 * 提供下拉刷新、滑动操作、触觉反馈、长按复制等移动端特定功能
 */

class MobileEnhancement {
    constructor() {
        this.isTouch = 'ontouchstart' in window;
        this.pullToRefreshEnabled = false;
        this.swipeThreshold = 80; // 滑动阈值
        this.longPressDelay = 800; // 长按延迟
        this.hapticSupported = 'vibrate' in navigator;
        
        this.init();
    }

    init() {
        if (this.isTouch) {
            this.initPullToRefresh();
            this.initSwipeActions();
            this.initLongPress();
            this.initCustomModal();
            this.initHapticFeedback();
            this.optimizeScrolling();
        }
    }

    // 初始化下拉刷新
    initPullToRefresh() {
        const messageList = document.querySelector('.message-list');
        if (!messageList) return;

        let startY = 0;
        let currentY = 0;
        let isPulling = false;
        let isRefreshing = false;

        const indicator = messageList.querySelector('.pull-to-refresh-indicator');
        const refreshThreshold = 60;

        messageList.addEventListener('touchstart', (e) => {
            if (messageList.scrollTop === 0 && !isRefreshing) {
                startY = e.touches[0].clientY;
                isPulling = true;
            }
        }, { passive: true });

        messageList.addEventListener('touchmove', (e) => {
            if (!isPulling || isRefreshing) return;

            currentY = e.touches[0].clientY;
            const pullDistance = currentY - startY;

            if (pullDistance > 0) {
                e.preventDefault();
                const pullRatio = Math.min(pullDistance / refreshThreshold, 1);
                
                if (pullDistance > refreshThreshold) {
                    messageList.classList.add('pulling');
                    indicator.innerHTML = '<i class="fas fa-arrow-up text-blue-500"></i>';
                } else {
                    messageList.classList.remove('pulling');
                    indicator.innerHTML = '<i class="fas fa-arrow-down text-blue-500"></i>';
                }

                indicator.style.transform = `translateX(-50%) translateY(${pullDistance * 0.5}px)`;
            }
        }, { passive: false });

        messageList.addEventListener('touchend', () => {
            if (!isPulling || isRefreshing) return;

            const pullDistance = currentY - startY;
            
            if (pullDistance > refreshThreshold) {
                this.triggerRefresh();
            } else {
                this.resetPullToRefresh();
            }

            isPulling = false;
        });
    }

    // 触发刷新
    async triggerRefresh() {
        const messageList = document.querySelector('.message-list');
        const indicator = messageList.querySelector('.pull-to-refresh-indicator');
        
        messageList.classList.add('refreshing');
        indicator.innerHTML = '<i class="fas fa-spinner fa-spin text-blue-500"></i>';
        
        this.hapticFeedback('light');

        try {
            // 调用刷新函数
            const refreshBtn = document.getElementById('refresh-btn');
            if (refreshBtn && !refreshBtn.disabled) {
                refreshBtn.click();
            }
            
            // 等待至少1秒以显示刷新动画
            await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
            console.error('Pull to refresh failed:', error);
        } finally {
            this.resetPullToRefresh();
        }
    }

    // 重置下拉刷新状态
    resetPullToRefresh() {
        const messageList = document.querySelector('.message-list');
        const indicator = messageList.querySelector('.pull-to-refresh-indicator');
        
        messageList.classList.remove('pulling', 'refreshing');
        indicator.innerHTML = '<i class="fas fa-arrow-down text-blue-500"></i>';
        indicator.style.transform = 'translateX(-50%)';
    }

    // 初始化滑动操作
    initSwipeActions() {
        let startX = 0;
        let currentX = 0;
        let isSwipping = false;
        let swipeTarget = null;

        document.addEventListener('touchstart', (e) => {
            const emailItem = e.target.closest('.email-item');
            if (emailItem) {
                startX = e.touches[0].clientX;
                isSwipping = true;
                swipeTarget = emailItem;
                swipeTarget.classList.add('swipe-item');
            }
        }, { passive: true });

        document.addEventListener('touchmove', (e) => {
            if (!isSwipping || !swipeTarget) return;

            currentX = e.touches[0].clientX;
            const swipeDistance = startX - currentX;

            if (swipeDistance > 0) {
                const swipeRatio = Math.min(swipeDistance / this.swipeThreshold, 1);
                swipeTarget.style.transform = `translateX(-${swipeDistance * 0.5}px)`;
                
                if (swipeDistance > this.swipeThreshold) {
                    swipeTarget.classList.add('swiped');
                    this.hapticFeedback('light');
                }
            }
        }, { passive: true });

        document.addEventListener('touchend', () => {
            if (!isSwipping || !swipeTarget) return;

            const swipeDistance = startX - currentX;
            
            if (swipeDistance > this.swipeThreshold) {
                this.showSwipeActions(swipeTarget);
            } else {
                this.resetSwipe(swipeTarget);
            }

            isSwipping = false;
            swipeTarget = null;
        });
    }

    // 显示滑动操作
    showSwipeActions(emailItem) {
        // 创建滑动操作按钮
        if (!emailItem.querySelector('.swipe-actions')) {
            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'swipe-actions';
            actionsDiv.innerHTML = `
                <div class="swipe-action mark" data-action="mark">
                    <i class="fas fa-star"></i>
                </div>
                <div class="swipe-action delete" data-action="delete">
                    <i class="fas fa-trash"></i>
                </div>
            `;
            emailItem.appendChild(actionsDiv);

            // 添加操作事件监听器
            actionsDiv.addEventListener('click', (e) => {
                const action = e.target.closest('.swipe-action');
                if (action) {
                    const actionType = action.dataset.action;
                    this.handleSwipeAction(emailItem, actionType);
                }
            });
        }

        emailItem.classList.add('swiped');
    }

    // 处理滑动操作
    handleSwipeAction(emailItem, action) {
        this.hapticFeedback('medium');
        
        switch (action) {
            case 'delete':
                this.deleteEmailItem(emailItem);
                break;
            case 'mark':
                this.markEmailItem(emailItem);
                break;
        }
    }

    // 删除邮件项
    deleteEmailItem(emailItem) {
        emailItem.style.transform = 'translateX(-100%)';
        emailItem.style.opacity = '0';
        
        setTimeout(() => {
            emailItem.remove();
        }, 300);
    }

    // 标记邮件项
    markEmailItem(emailItem) {
        emailItem.classList.toggle('marked');
        this.resetSwipe(emailItem);
    }

    // 重置滑动状态
    resetSwipe(emailItem) {
        emailItem.style.transform = '';
        emailItem.classList.remove('swiped', 'swipe-item');
        
        const actions = emailItem.querySelector('.swipe-actions');
        if (actions) {
            actions.remove();
        }
    }

    // 初始化长按功能
    initLongPress() {
        const emailBox = document.querySelector('.email-box');
        if (!emailBox) return;

        let longPressTimer = null;
        let isLongPress = false;

        emailBox.addEventListener('touchstart', () => {
            isLongPress = false;
            longPressTimer = setTimeout(() => {
                isLongPress = true;
                this.showLongPressHint();
                this.hapticFeedback('medium');
                this.copyEmailAddress();
            }, this.longPressDelay);
        });

        emailBox.addEventListener('touchend', () => {
            clearTimeout(longPressTimer);
            if (isLongPress) {
                this.hideLongPressHint();
            }
        });

        emailBox.addEventListener('touchmove', () => {
            clearTimeout(longPressTimer);
        });
    }

    // 显示长按提示
    showLongPressHint() {
        const emailBox = document.querySelector('.email-box');
        emailBox.classList.add('show-hint');
    }

    // 隐藏长按提示
    hideLongPressHint() {
        const emailBox = document.querySelector('.email-box');
        emailBox.classList.remove('show-hint');
    }

    // 复制邮箱地址
    async copyEmailAddress() {
        const emailAddress = document.getElementById('email-address').textContent;
        if (emailAddress) {
            try {
                await navigator.clipboard.writeText(emailAddress);
                this.showToast('邮箱地址已复制');
            } catch (error) {
                console.error('Copy failed:', error);
            }
        }
    }

    // 初始化自定义模态框
    initCustomModal() {
        const customBtn = document.getElementById('custom-email-btn');
        const modal = document.getElementById('custom-prefix-modal');
        const closeBtn = document.getElementById('close-custom-modal');
        const cancelBtn = document.getElementById('cancel-custom-btn');
        const confirmBtn = document.getElementById('confirm-custom-btn');
        const input = document.getElementById('custom-prefix-input');
        const errorDiv = document.getElementById('prefix-error');

        if (!customBtn || !modal) return;

        // 打开模态框
        customBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.showCustomModal();
        });

        // 关闭模态框
        [closeBtn, cancelBtn].forEach(btn => {
            if (btn) {
                btn.addEventListener('click', () => {
                    this.hideCustomModal();
                });
            }
        });

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideCustomModal();
            }
        });

        // 确认按钮
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.handleCustomPrefix();
            });
        }

        // 输入验证
        if (input) {
            input.addEventListener('input', () => {
                this.validatePrefix(input.value);
            });

            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleCustomPrefix();
                }
            });
        }
    }

    // 显示自定义模态框
    showCustomModal() {
        const modal = document.getElementById('custom-prefix-modal');
        const input = document.getElementById('custom-prefix-input');
        
        modal.classList.remove('hidden');
        
        // 延迟聚焦以确保模态框完全显示
        setTimeout(() => {
            if (input) {
                input.focus();
                input.value = '';
            }
        }, 100);
    }

    // 隐藏自定义模态框
    hideCustomModal() {
        const modal = document.getElementById('custom-prefix-modal');
        const input = document.getElementById('custom-prefix-input');
        const errorDiv = document.getElementById('prefix-error');
        
        modal.classList.add('hidden');
        
        if (input) {
            input.value = '';
            input.blur();
        }
        
        if (errorDiv) {
            errorDiv.classList.add('hidden');
        }
    }

    // 验证前缀
    validatePrefix(prefix) {
        const errorDiv = document.getElementById('prefix-error');
        const confirmBtn = document.getElementById('confirm-custom-btn');
        
        if (!prefix) {
            errorDiv.textContent = '前缀不能为空';
            errorDiv.classList.remove('hidden');
            confirmBtn.disabled = true;
            return false;
        }

        if (prefix.length > 20) {
            errorDiv.textContent = '前缀长度不能超过20个字符';
            errorDiv.classList.remove('hidden');
            confirmBtn.disabled = true;
            return false;
        }

        const prefixRegex = /^[a-zA-Z0-9\-]{1,20}$/;
        if (!prefixRegex.test(prefix)) {
            errorDiv.textContent = '前缀只能包含字母、数字和连字符';
            errorDiv.classList.remove('hidden');
            confirmBtn.disabled = true;
            return false;
        }

        errorDiv.classList.add('hidden');
        confirmBtn.disabled = false;
        return true;
    }

    // 处理自定义前缀
    async handleCustomPrefix() {
        const input = document.getElementById('custom-prefix-input');
        const prefix = input.value.trim();
        
        if (!this.validatePrefix(prefix)) {
            return;
        }

        this.hideCustomModal();
        this.hapticFeedback('medium');

        // 触发自定义邮箱生成
        if (window.mobileCustomPrefixCallback) {
            window.mobileCustomPrefixCallback(prefix);
        }
    }

    // 初始化触觉反馈
    initHapticFeedback() {
        // 为所有带有 haptic-feedback 类的元素添加触觉反馈
        document.addEventListener('touchstart', (e) => {
            if (e.target.closest('.haptic-feedback')) {
                this.hapticFeedback('light');
            }
        }, { passive: true });
    }

    // 触觉反馈
    hapticFeedback(type = 'light') {
        if (!this.hapticSupported) return;

        const patterns = {
            light: 10,
            medium: 20,
            heavy: 50
        };

        navigator.vibrate(patterns[type] || patterns.light);
    }

    // 优化滚动
    optimizeScrolling() {
        // 为所有滚动容器添加平滑滚动
        const scrollContainers = document.querySelectorAll('.scroll-smooth');
        scrollContainers.forEach(container => {
            container.style.webkitOverflowScrolling = 'touch';
        });
    }

    // 显示提示消息
    showToast(message, duration = 2000) {
        const toast = document.createElement('div');
        toast.className = 'fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50 transition-opacity duration-300';
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, duration);
    }

    // 调整移动端轮询间隔
    adjustPollingForMobile() {
        // 在移动端降低轮询频率以节省电池
        if (this.isTouch && window.appConfig) {
            const originalInterval = window.appConfig.AUTO_REFRESH_INTERVAL || 10000;
            window.appConfig.AUTO_REFRESH_INTERVAL = Math.max(originalInterval * 1.5, 15000);
        }
    }
}

// 初始化移动端增强功能
document.addEventListener('DOMContentLoaded', () => {
    window.mobileEnhancement = new MobileEnhancement();
});
